<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初中英语词性转换练习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .mode-selector {
            display: flex;
            gap: 10px;
        }

        .mode-btn {
            padding: 8px 16px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .mode-btn.active {
            background: #007bff;
            color: white;
        }

        .stats {
            display: flex;
            gap: 20px;
            font-weight: bold;
        }

        .stat-item {
            color: #666;
        }

        .exercise-area {
            padding: 40px;
            min-height: 400px;
        }

        .question {
            text-align: center;
            margin-bottom: 30px;
        }

        .question h2 {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
        }

        .word-display {
            font-size: 2.5em;
            color: #007bff;
            font-weight: bold;
            margin: 20px 0;
        }

        .pos-hint {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 30px;
        }

        .input-area {
            margin: 30px 0;
        }

        .answer-input {
            width: 100%;
            max-width: 400px;
            padding: 15px;
            font-size: 1.5em;
            border: 3px solid #ddd;
            border-radius: 10px;
            text-align: center;
            transition: border-color 0.3s;
        }

        .answer-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            font-size: 1.1em;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .feedback {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 1.1em;
        }

        .feedback.correct {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .feedback.incorrect {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .word-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .word-group h3 {
            color: #007bff;
            margin-bottom: 15px;
        }

        .word-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .word-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }

        @media (max-width: 600px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .mode-selector {
                justify-content: center;
            }

            .stats {
                justify-content: center;
            }

            .buttons {
                flex-direction: column;
                align-items: center;
            }

            .word-display {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 初中英语词性转换练习</h1>
            <p>掌握词性转换，提升英语水平</p>
        </div>

        <div class="controls">
            <div class="mode-selector">
                <button class="mode-btn active" data-mode="practice">练习模式</button>
                <button class="mode-btn" data-mode="test">测试模式</button>
                <button class="mode-btn" data-mode="review">复习模式</button>
            </div>
            <div class="stats">
                <div class="stat-item">正确: <span id="correct-count">0</span></div>
                <div class="stat-item">错误: <span id="wrong-count">0</span></div>
                <div class="stat-item">准确率: <span id="accuracy">0%</span></div>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>

        <div class="exercise-area">
            <div id="practice-mode" class="mode-content">
                <div class="question">
                    <h2>请写出下列单词的指定词性形式：</h2>
                    <div class="word-display" id="current-word">ability</div>
                    <div class="pos-hint" id="pos-hint">请写出其形容词形式</div>
                </div>

                <div class="input-area">
                    <input type="text" class="answer-input" id="answer-input" placeholder="请输入答案..." autocomplete="off">
                </div>

                <div class="buttons">
                    <button class="btn btn-primary" id="check-btn">检查答案</button>
                    <button class="btn btn-secondary" id="skip-btn">跳过</button>
                    <button class="btn btn-success" id="next-btn" style="display: none;">下一题</button>
                </div>

                <div id="feedback" class="feedback" style="display: none;"></div>
            </div>

            <div id="review-mode" class="mode-content" style="display: none;">
                <h2 style="text-align: center; margin-bottom: 30px;">📚 词汇复习</h2>
                <div id="word-groups"></div>
            </div>
        </div>
    </div>

    <script>
        // 词汇数据
        const wordData = [
            {
                group: "ability系列",
                words: [
                    { word: "ability", pos: "n.", meaning: "能力" },
                    { word: "able", pos: "adj.", meaning: "能够的" },
                    { word: "enable", pos: "v.", meaning: "使能够" },
                    { word: "disable", pos: "v.", meaning: "使残疾" },
                    { word: "disabled", pos: "adj.", meaning: "残疾的" },
                    { word: "unable", pos: "adj.", meaning: "不能的" }
                ]
            },
            {
                group: "accept系列",
                words: [
                    { word: "accept", pos: "v.", meaning: "接受" },
                    { word: "acceptable", pos: "adj.", meaning: "可接受的" }
                ]
            },
            {
                group: "accident系列",
                words: [
                    { word: "accident", pos: "n.", meaning: "事故" },
                    { word: "accidental", pos: "adj.", meaning: "意外的" },
                    { word: "accidentally", pos: "adv.", meaning: "意外地" }
                ]
            },
            {
                group: "accurate系列",
                words: [
                    { word: "accurate", pos: "adj.", meaning: "准确的" },
                    { word: "accuracy", pos: "n.", meaning: "准确性" },
                    { word: "accurately", pos: "adv.", meaning: "准确地" }
                ]
            },
            {
                group: "achieve系列",
                words: [
                    { word: "achieve", pos: "v.", meaning: "实现" },
                    { word: "achievement", pos: "n.", meaning: "成就" }
                ]
            },
            {
                group: "act系列",
                words: [
                    { word: "act", pos: "v.", meaning: "行动" },
                    { word: "action", pos: "n.", meaning: "行动" },
                    { word: "active", pos: "adj.", meaning: "积极的" },
                    { word: "activity", pos: "n.", meaning: "活动" },
                    { word: "actor", pos: "n.", meaning: "演员" },
                    { word: "actress", pos: "n.", meaning: "女演员" }
                ]
            },
            {
                group: "actual系列",
                words: [
                    { word: "actually", pos: "adv.", meaning: "实际上" },
                    { word: "actual", pos: "adj.", meaning: "实际的" }
                ]
            },
            {
                group: "add系列",
                words: [
                    { word: "add", pos: "v.", meaning: "添加" },
                    { word: "addition", pos: "n.", meaning: "添加" }
                ]
            },
            {
                group: "advertise系列",
                words: [
                    { word: "advertisement", pos: "n.", meaning: "广告" },
                    { word: "advertise", pos: "v.", meaning: "做广告" }
                ]
            },
            {
                group: "advice系列",
                words: [
                    { word: "advice", pos: "n.", meaning: "建议" },
                    { word: "advise", pos: "v.", meaning: "建议" }
                ]
            },
            {
                group: "Africa系列",
                words: [
                    { word: "Africa", pos: "n.", meaning: "非洲" },
                    { word: "African", pos: "adj.", meaning: "非洲的" }
                ]
            },
            {
                group: "age系列",
                words: [
                    { word: "age", pos: "n.", meaning: "年龄" },
                    { word: "aged", pos: "adj.", meaning: "年老的" }
                ]
            },
            {
                group: "agree系列",
                words: [
                    { word: "agree", pos: "v.", meaning: "同意" },
                    { word: "agreement", pos: "n.", meaning: "协议" },
                    { word: "disagree", pos: "v.", meaning: "不同意" }
                ]
            },
            {
                group: "live系列",
                words: [
                    { word: "alive", pos: "adj.", meaning: "活着的" },
                    { word: "live", pos: "v.", meaning: "生活" },
                    { word: "living", pos: "adj.", meaning: "活的" },
                    { word: "life", pos: "n.", meaning: "生活" }
                ]
            },
            {
                group: "amaze系列",
                words: [
                    { word: "amazing", pos: "adj.", meaning: "令人惊奇的" },
                    { word: "amaze", pos: "v.", meaning: "使惊奇" }
                ]
            },
            {
                group: "ambition系列",
                words: [
                    { word: "ambition", pos: "n.", meaning: "雄心" },
                    { word: "ambitious", pos: "adj.", meaning: "有雄心的" }
                ]
            },
            {
                group: "America系列",
                words: [
                    { word: "America", pos: "n.", meaning: "美国" },
                    { word: "American", pos: "adj.", meaning: "美国的" }
                ]
            },
            {
                group: "amuse系列",
                words: [
                    { word: "amusement", pos: "n.", meaning: "娱乐" },
                    { word: "amuse", pos: "v.", meaning: "娱乐" },
                    { word: "amusing", pos: "adj.", meaning: "有趣的" }
                ]
            },
            {
                group: "angry系列",
                words: [
                    { word: "angrily", pos: "adv.", meaning: "愤怒地" },
                    { word: "angry", pos: "adj.", meaning: "愤怒的" }
                ]
            },
            {
                group: "apologize系列",
                words: [
                    { word: "apologize", pos: "v.", meaning: "道歉" },
                    { word: "apology", pos: "n.", meaning: "道歉" }
                ]
            },
            {
                group: "appear系列",
                words: [
                    { word: "appear", pos: "v.", meaning: "出现" },
                    { word: "appearance", pos: "n.", meaning: "外观" },
                    { word: "disappear", pos: "v.", meaning: "消失" }
                ]
            },
            {
                group: "apply系列",
                words: [
                    { word: "apply", pos: "v.", meaning: "申请" },
                    { word: "application", pos: "n.", meaning: "申请" }
                ]
            },
            {
                group: "argue系列",
                words: [
                    { word: "argue", pos: "v.", meaning: "争论" },
                    { word: "argument", pos: "n.", meaning: "争论" }
                ]
            },
            {
                group: "arrange系列",
                words: [
                    { word: "arrange", pos: "v.", meaning: "安排" },
                    { word: "arrangement", pos: "n.", meaning: "安排" }
                ]
            },
            {
                group: "arrive系列",
                words: [
                    { word: "arrive", pos: "v.", meaning: "到达" },
                    { word: "arrival", pos: "n.", meaning: "到达" }
                ]
            },
            {
                group: "art系列",
                words: [
                    { word: "art", pos: "n.", meaning: "艺术" },
                    { word: "artist", pos: "n.", meaning: "艺术家" },
                    { word: "artistic", pos: "adj.", meaning: "艺术的" }
                ]
            },
            {
                group: "Asia系列",
                words: [
                    { word: "Asia", pos: "n.", meaning: "亚洲" },
                    { word: "Asian", pos: "adj.", meaning: "亚洲的" }
                ]
            },
            {
                group: "attract系列",
                words: [
                    { word: "attract", pos: "v.", meaning: "吸引" },
                    { word: "attraction", pos: "n.", meaning: "吸引力" },
                    { word: "attractive", pos: "adj.", meaning: "有吸引力的" },
                    { word: "attractively", pos: "adv.", meaning: "有吸引力地" }
                ]
            },
            {
                group: "Australia系列",
                words: [
                    { word: "Australia", pos: "n.", meaning: "澳大利亚" },
                    { word: "Australian", pos: "adj.", meaning: "澳大利亚的" }
                ]
            },
            {
                group: "automatic系列",
                words: [
                    { word: "automatic", pos: "adj.", meaning: "自动的" },
                    { word: "automatically", pos: "adv.", meaning: "自动地" }
                ]
            },
            {
                group: "available系列",
                words: [
                    { word: "available", pos: "adj.", meaning: "可用的" },
                    { word: "availability", pos: "n.", meaning: "可用性" }
                ]
            },
            {
                group: "average系列",
                words: [
                    { word: "average", pos: "adj.", meaning: "平均的" },
                    { word: "averagely", pos: "adv.", meaning: "平均地" }
                ]
            },
            {
                group: "awful系列",
                words: [
                    { word: "awful", pos: "adj.", meaning: "可怕的" },
                    { word: "awfully", pos: "adv.", meaning: "可怕地" }
                ]
            },
            {
                group: "bad系列",
                words: [
                    { word: "bad", pos: "adj.", meaning: "坏的" },
                    { word: "badly", pos: "adv.", meaning: "坏地" }
                ]
            },
            {
                group: "bake系列",
                words: [
                    { word: "bakery", pos: "n.", meaning: "面包店" },
                    { word: "bake", pos: "v.", meaning: "烘烤" },
                    { word: "baker", pos: "n.", meaning: "面包师" }
                ]
            },
            {
                group: "balance系列",
                words: [
                    { word: "balance", pos: "n.", meaning: "平衡" },
                    { word: "balanced", pos: "adj.", meaning: "平衡的" }
                ]
            },
            {
                group: "basic系列",
                words: [
                    { word: "basic", pos: "adj.", meaning: "基本的" },
                    { word: "base", pos: "n.", meaning: "基础" },
                    { word: "basically", pos: "adv.", meaning: "基本上" },
                    { word: "basics", pos: "n.", meaning: "基础知识" }
                ]
            },
            {
                group: "bath系列",
                words: [
                    { word: "bath", pos: "n.", meaning: "洗澡" },
                    { word: "bathe", pos: "v.", meaning: "洗澡" }
                ]
            },
            {
                group: "beautiful系列",
                words: [
                    { word: "beautiful", pos: "adj.", meaning: "美丽的" },
                    { word: "beautifully", pos: "adv.", meaning: "美丽地" },
                    { word: "beauty", pos: "n.", meaning: "美丽" }
                ]
            },
            {
                group: "beg系列",
                words: [
                    { word: "beg", pos: "v.", meaning: "乞求" },
                    { word: "beggar", pos: "n.", meaning: "乞丐" }
                ]
            },
            {
                group: "begin系列",
                words: [
                    { word: "begin", pos: "v.", meaning: "开始" },
                    { word: "beginning", pos: "n.", meaning: "开始" }
                ]
            },
            {
                group: "behave系列",
                words: [
                    { word: "behaviour", pos: "n.", meaning: "行为" },
                    { word: "behave", pos: "v.", meaning: "表现" }
                ]
            },
            {
                group: "believe系列",
                words: [
                    { word: "believe", pos: "v.", meaning: "相信" },
                    { word: "believable", pos: "adj.", meaning: "可信的" },
                    { word: "unbelievable", pos: "adj.", meaning: "难以置信的" },
                    { word: "believably", pos: "adv.", meaning: "可信地" },
                    { word: "belief", pos: "n.", meaning: "信念" }
                ]
            },
            {
                group: "board系列",
                words: [
                    { word: "blackboard", pos: "n.", meaning: "黑板" },
                    { word: "board", pos: "n.", meaning: "板子" }
                ]
            },
            {
                group: "boil系列",
                words: [
                    { word: "boil", pos: "v.", meaning: "煮沸" },
                    { word: "boiled", pos: "adj.", meaning: "煮熟的" },
                    { word: "boiling", pos: "adj.", meaning: "沸腾的" }
                ]
            },
            {
                group: "bore系列",
                words: [
                    { word: "bored", pos: "adj.", meaning: "无聊的" },
                    { word: "boring", pos: "adj.", meaning: "令人无聊的" }
                ]
            },
            {
                group: "brave系列",
                words: [
                    { word: "brave", pos: "adj.", meaning: "勇敢的" },
                    { word: "bravely", pos: "adv.", meaning: "勇敢地" },
                    { word: "bravery", pos: "n.", meaning: "勇敢" }
                ]
            },
            {
                group: "breath系列",
                words: [
                    { word: "breath", pos: "n.", meaning: "呼吸" },
                    { word: "breathe", pos: "v.", meaning: "呼吸" }
                ]
            },
            {
                group: "bright系列",
                words: [
                    { word: "bright", pos: "adj.", meaning: "明亮的" },
                    { word: "brightly", pos: "adv.", meaning: "明亮地" }
                ]
            },
            {
                group: "Britain系列",
                words: [
                    { word: "Britain", pos: "n.", meaning: "英国" },
                    { word: "British", pos: "adj.", meaning: "英国的" },
                    { word: "Briton", pos: "n.", meaning: "英国人" }
                ]
            },
            {
                group: "build系列",
                words: [
                    { word: "builder", pos: "n.", meaning: "建筑工人" },
                    { word: "building", pos: "n.", meaning: "建筑物" },
                    { word: "rebuild", pos: "v.", meaning: "重建" }
                ]
            },
            {
                group: "business系列",
                words: [
                    { word: "business", pos: "n.", meaning: "商业" },
                    { word: "businessman", pos: "n.", meaning: "商人" },
                    { word: "businesswoman", pos: "n.", meaning: "女商人" },
                    { word: "busy", pos: "adj.", meaning: "忙碌的" }
                ]
            },
            {
                group: "calm系列",
                words: [
                    { word: "calm", pos: "adj.", meaning: "平静的" },
                    { word: "calmly", pos: "adv.", meaning: "平静地" }
                ]
            },
            {
                group: "Canada系列",
                words: [
                    { word: "Canada", pos: "n.", meaning: "加拿大" },
                    { word: "Canadian", pos: "adj.", meaning: "加拿大的" }
                ]
            },
            {
                group: "care系列",
                words: [
                    { word: "care", pos: "n.", meaning: "关心" },
                    { word: "careful", pos: "adj.", meaning: "小心的" },
                    { word: "carefully", pos: "adv.", meaning: "小心地" },
                    { word: "careless", pos: "adj.", meaning: "粗心的" }
                ]
            },
            {
                group: "celebrate系列",
                words: [
                    { word: "celebrate", pos: "v.", meaning: "庆祝" },
                    { word: "celebration", pos: "n.", meaning: "庆祝" },
                    { word: "celebratory", pos: "adj.", meaning: "庆祝的" }
                ]
            },
            {
                group: "centre系列",
                words: [
                    { word: "centre", pos: "n.", meaning: "中心" },
                    { word: "central", pos: "adj.", meaning: "中央的" }
                ]
            },
            {
                group: "certain系列",
                words: [
                    { word: "certainly", pos: "adv.", meaning: "当然" },
                    { word: "certain", pos: "adj.", meaning: "确定的" }
                ]
            }
        ];

        // 练习状态
        let currentMode = 'practice';
        let currentQuestion = 0;
        let correctCount = 0;
        let wrongCount = 0;
        let currentWordGroup = null;
        let currentTargetWord = null;

        // DOM元素
        const modeButtons = document.querySelectorAll('.mode-btn');
        const modeContents = document.querySelectorAll('.mode-content');
        const currentWordEl = document.getElementById('current-word');
        const posHintEl = document.getElementById('pos-hint');
        const answerInput = document.getElementById('answer-input');
        const checkBtn = document.getElementById('check-btn');
        const skipBtn = document.getElementById('skip-btn');
        const nextBtn = document.getElementById('next-btn');
        const feedbackEl = document.getElementById('feedback');
        const correctCountEl = document.getElementById('correct-count');
        const wrongCountEl = document.getElementById('wrong-count');
        const accuracyEl = document.getElementById('accuracy');
        const progressFill = document.getElementById('progress-fill');

        // 初始化
        init();

        function init() {
            setupEventListeners();
            generateNextQuestion();
            updateStats();
        }

        function setupEventListeners() {
            // 模式切换
            modeButtons.forEach(btn => {
                btn.addEventListener('click', () => switchMode(btn.dataset.mode));
            });

            // 答案检查
            checkBtn.addEventListener('click', checkAnswer);
            nextBtn.addEventListener('click', generateNextQuestion);
            skipBtn.addEventListener('click', skipQuestion);

            // 回车键提交
            answerInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    if (checkBtn.style.display !== 'none') {
                        checkAnswer();
                    } else {
                        generateNextQuestion();
                    }
                }
            });
        }

        function switchMode(mode) {
            currentMode = mode;
            
            // 更新按钮状态
            modeButtons.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.mode === mode);
            });

            // 显示对应内容
            if (mode === 'review') {
                document.getElementById('practice-mode').style.display = 'none';
                document.getElementById('review-mode').style.display = 'block';
                showReviewMode();
            } else {
                document.getElementById('practice-mode').style.display = 'block';
                document.getElementById('review-mode').style.display = 'none';
                generateNextQuestion();
            }
        }

        function generateNextQuestion() {
            // 随机选择一个词汇组
            const randomGroup = wordData[Math.floor(Math.random() * wordData.length)];
            currentWordGroup = randomGroup;
            
            // 随机选择组内的一个词作为题目
            const sourceWord = randomGroup.words[Math.floor(Math.random() * randomGroup.words.length)];
            
            // 随机选择组内的另一个词作为答案
            const targetWords = randomGroup.words.filter(w => w.word !== sourceWord.word);
            if (targetWords.length === 0) return generateNextQuestion();
            
            currentTargetWord = targetWords[Math.floor(Math.random() * targetWords.length)];
            
            // 更新显示
            currentWordEl.textContent = sourceWord.word;
            posHintEl.textContent = `请写出其${getPosName(currentTargetWord.pos)}形式`;
            
            // 重置输入
            answerInput.value = '';
            answerInput.focus();
            
            // 重置按钮状态
            checkBtn.style.display = 'inline-block';
            nextBtn.style.display = 'none';
            feedbackEl.style.display = 'none';
            
            currentQuestion++;
            updateProgress();
        }

        function checkAnswer() {
            const userAnswer = answerInput.value.trim().toLowerCase();
            const correctAnswer = currentTargetWord.word.toLowerCase();
            
            if (userAnswer === correctAnswer) {
                showFeedback(true, `正确！${currentTargetWord.word} (${currentTargetWord.meaning})`);
                correctCount++;
            } else {
                showFeedback(false, `错误！正确答案是：${currentTargetWord.word} (${currentTargetWord.meaning})`);
                wrongCount++;
            }
            
            updateStats();
            checkBtn.style.display = 'none';
            nextBtn.style.display = 'inline-block';
            nextBtn.focus();
        }

        function skipQuestion() {
            showFeedback(false, `跳过！正确答案是：${currentTargetWord.word} (${currentTargetWord.meaning})`);
            wrongCount++;
            updateStats();
            checkBtn.style.display = 'none';
            nextBtn.style.display = 'inline-block';
        }

        function showFeedback(isCorrect, message) {
            feedbackEl.textContent = message;
            feedbackEl.className = `feedback ${isCorrect ? 'correct' : 'incorrect'}`;
            feedbackEl.style.display = 'block';
        }

        function updateStats() {
            correctCountEl.textContent = correctCount;
            wrongCountEl.textContent = wrongCount;
            
            const total = correctCount + wrongCount;
            const accuracy = total > 0 ? Math.round((correctCount / total) * 100) : 0;
            accuracyEl.textContent = accuracy + '%';
        }

        function updateProgress() {
            const progress = Math.min((currentQuestion / 20) * 100, 100);
            progressFill.style.width = progress + '%';
        }

        function getPosName(pos) {
            const posMap = {
                'n.': '名词',
                'v.': '动词', 
                'adj.': '形容词',
                'adv.': '副词',
                'prep.': '介词',
                'prep. & adv.': '介词/副词',
                'v. & n.': '动词/名词',
                'adj. & n.': '形容词/名词'
            };
            return posMap[pos] || pos;
        }

        function showReviewMode() {
            const reviewContainer = document.getElementById('word-groups');
            reviewContainer.innerHTML = '';
            
            wordData.forEach(group => {
                const groupDiv = document.createElement('div');
                groupDiv.className = 'word-group';
                
                const title = document.createElement('h3');
                title.textContent = group.group;
                groupDiv.appendChild(title);
                
                const wordList = document.createElement('div');
                wordList.className = 'word-list';
                
                group.words.forEach(word => {
                    const wordItem = document.createElement('div');
                    wordItem.className = 'word-item';
                    wordItem.innerHTML = `<strong>${word.word}</strong> <em>${word.pos}</em><br><small>${word.meaning}</small>`;
                    wordList.appendChild(wordItem);
                });
                
                groupDiv.appendChild(wordList);
                reviewContainer.appendChild(groupDiv);
            });
        }
    </script>
</body>
</html>
