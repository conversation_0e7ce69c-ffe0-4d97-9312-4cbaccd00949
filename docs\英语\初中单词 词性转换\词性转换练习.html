<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初中英语词性转换练习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .mode-selector {
            display: flex;
            gap: 10px;
        }

        .mode-btn {
            padding: 8px 16px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .mode-btn.active {
            background: #007bff;
            color: white;
        }

        .stats {
            display: flex;
            gap: 20px;
            font-weight: bold;
        }

        .stat-item {
            color: #666;
        }

        .exercise-area {
            padding: 40px;
            min-height: 400px;
        }

        .question {
            text-align: center;
            margin-bottom: 30px;
        }

        .question h2 {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
        }

        .word-display {
            font-size: 2.5em;
            color: #007bff;
            font-weight: bold;
            margin: 20px 0;
        }

        .pos-hint {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 30px;
        }

        .input-area {
            margin: 30px 0;
        }

        .answer-input {
            width: 100%;
            max-width: 400px;
            padding: 15px;
            font-size: 1.5em;
            border: 3px solid #ddd;
            border-radius: 10px;
            text-align: center;
            transition: border-color 0.3s;
        }

        .answer-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            font-size: 1.1em;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .feedback {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 1.1em;
        }

        .feedback.correct {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .feedback.incorrect {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .word-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .word-group h3 {
            color: #007bff;
            margin-bottom: 15px;
        }

        .word-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .word-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }

        @media (max-width: 600px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .mode-selector {
                justify-content: center;
            }

            .stats {
                justify-content: center;
            }

            .buttons {
                flex-direction: column;
                align-items: center;
            }

            .word-display {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 初中英语词性转换练习</h1>
            <p>掌握词性转换，提升英语水平</p>
        </div>

        <div class="controls">
            <div class="mode-selector">
                <button class="mode-btn active" data-mode="practice">练习模式</button>
                <button class="mode-btn" data-mode="test">测试模式</button>
                <button class="mode-btn" data-mode="review">复习模式</button>
            </div>
            <div class="stats">
                <div class="stat-item">正确: <span id="correct-count">0</span></div>
                <div class="stat-item">错误: <span id="wrong-count">0</span></div>
                <div class="stat-item">准确率: <span id="accuracy">0%</span></div>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>

        <div class="exercise-area">
            <div id="practice-mode" class="mode-content">
                <div class="question">
                    <h2>请写出下列单词的指定词性形式：</h2>
                    <div class="word-display" id="current-word">ability</div>
                    <div class="pos-hint" id="pos-hint">请写出其形容词形式</div>
                </div>

                <div class="input-area">
                    <input type="text" class="answer-input" id="answer-input" placeholder="请输入答案..." autocomplete="off">
                </div>

                <div class="buttons">
                    <button class="btn btn-primary" id="check-btn">检查答案</button>
                    <button class="btn btn-secondary" id="skip-btn">跳过</button>
                    <button class="btn btn-success" id="next-btn" style="display: none;">下一题</button>
                </div>

                <div id="feedback" class="feedback" style="display: none;"></div>
            </div>

            <div id="review-mode" class="mode-content" style="display: none;">
                <h2 style="text-align: center; margin-bottom: 30px;">📚 词汇复习</h2>
                <div id="word-groups"></div>
            </div>
        </div>
    </div>

    <script>
        // 从文件中解析的词汇数据
        function parseWordData() {
            const rawData = `ability	n.
able	adj.
enable	v.
disable	v.
disabled	adj.
unable	adj.

accept	v.
acceptable	adj.

accident	n.
accidental	adj.
accidentally	adv.

accurate	adj.
accuracy	n.
accurately	adv.

achieve	v.
achievement	n.

across	prep. & adv.
cross	v. & n.

act	v. & n.
action	n.
active	adj.
activity	n.
actor	n.
actress	n.

actually	adv.
actual		adj.

add	v.
addition	n.

advertisement	n.
advertise	v.

advice	n.
advise	v.

Africa	n.
African	adj.

age	n.
aged	adj.

agree	v.
agreement	n.
disagree	v.

alive	adj.
live	v.
living	adj.
life	n.

amazing	adj.
amaze	v.

ambition	n.
ambitious	adj.

America	n.
American	adj. & n.

amusement	n.
amuse		v.
amusing		adj.

angrily	adv.
angry	adj.

apologize	v.
apology		n.

appear	v.
appearance	n.
disappear	v.

apply	v.
application	n.

argue	v.
argument	n.

arrange	v.
arrangement	n.

arrive	v.
arrival	n.

art	n.
artist	n.
artistic	adj.

Asia	n.
Asian	adj.

attract	v.
attraction	n.
attractive	adj.
attractively	adv.

Australia	n.
Australian	adj. & n.

automatic	adj.
automatically	adv.

available	adj.
availability	n.

average	adj. & n.
averagely	adv.

awful	adj.
awfully	adv.

bad	adj.
badly	adv.

bakery	n.
bake	v.
baker	n.

balance	n.
balanced	adj.

basic	adj.
base	n.
basically	adv.
basics	n.

bath	n.
bathe	v.

beautiful	adj.
beautifully	adv.
beauty	n.

beg	v.
beggar	n.

begin	v.
beginning	n.

behaviour	n.
behave	v.

believe	v.
believable	adj.
unbelievable	adj.
believably	adv.
belief	n.

blackboard	n.
board	n. & v.

boil	v.
boiled	adj.
boiling	adj.

bored	adj.
boring	adj.

brave	adj.
bravely	adv.
bravery	n.

breath	n.
breathe	v.

bright	adj.
brightly	adv.

Britain	n.
British	adj. & n.
Briton	n.

builder	n.
building	n.
rebuild	v.

business	n.
businessman	n.
businesswoman	n.
busy	adj.

calm	adj.
calmly	adv.

Canada	n.
Canadian	adj. & n.

care	n. & v.
careful	adj.
carefully 	adv.
careless	adj.

celebrate	v.
celebration	n.
celebratory	adj.

centre	n.
central	adj.

certainly	adv.
certain	adj.

change	n.
changeable	adj.
changer	n.
exchange	v.

chemical	n. & adj.
chemist	n.
chemistry	n.

China	n.
Chinese	n. & adj.

choice	n.
choose	v.

citizen	n.
city	n.

clear	adj.
clearly	adv.

cloud	n.
cloudy	adj.

fog	n.
foggy	adj.

rain	n.
rainy	adj.

snow	n.
snowy	adj.

sun	n.
sunny	adj.

wind	n.
windy	adj.

collect	v.
collection	n.

colour	n.
colourful	adj.
colourless	adj.

comfortable	adj.
comfort	v.
comfortably	adv.

communicate	v.
communication	n.
communicative	adj.
communicatively	adv.

complain	v.
complaint	n.

complete	adj.
completely	adv.

conclusion	n.
conclude	v.

confidence	n.
confident	adj.

confuse	v.
confused	adj.
confusing	adj.
confusion	n.

congratulation	n.
congratulate	v.

connect	v.
connection	n.

consider	v.
considerate	adj.
consideration	n.

continue	v.
continuous	adj.

convenient	adj.
convenience	n.

cook	n.
cooked	adj.
cooker	n.
cookery	n.

correct	adj.
correction	n.
correctly	adv.
incorrect	adj.
uncorrected	adj.

count	v.
countless	adj.

cover	v.
coverage	n.

create	v.
creative adj.

crowd	n.
crowded adj.

culture	n.
cultural	adj.

custom	n.
customer	n.
customs	n.

cycle	v.
cycling	n.
cyclist	n.
recycle	v.

dance	n. & v.
dancer	n.

danger	n.
dangerous	adj.
dangerously	adv.

dark	adj.
darkness	n.

dead	adj.
death	n.
die	v.
dying	adj.

decide	v.
decision	n.

decorate	v.
decoration	n.

deep	adj.
depth	n.

departure	n.
depart	v.

depend	v.
dependence	n.
dependent	adj.
independent	adj.

describe	v.
description	n.

design	v.
designer	n.

desire	n.
desirable	adj.

develop	v.
developed adj.
developing	adj.
development	n.

difference	n.
different	adj.

difficult	adj.
difficulty	n.

direction	n.
direct	adj.
directly	adv.
director	n.

disappointed	adj.
disappoint	v.
disappointing	adj.
disappointment	n.

discover	v.
discovery	n.

discuss	v.
discussion n.

draw	v.
drawing	n.

drive	v.
driver	n.

easily	adv.
easy	adj.

education	n.
educate	v.
educational	adj.
educator	n.

effect	n.
effective	adj.

elder	adj.
elderly	adj.

electric	adj.
electrical	adj.
electricity	n.
electronic	adj.

embarrassed	adj.
embarrass	v.
embarrassment	n.
embarrassing	adj.

encourage	v.
encouragement	n.

engine	n.
engineer	n

England	n.
English	adj. & n.

enjoy	v.
enjoyable	adj.
joy	n.

enrich	v.
rich	adj.

enter	v.
entrance	n.

entertainment	n.
entertain	v.

environment	n.
environmental	adj.

especially	adv.
especial	adj.

Europe	n.
European	adj. & n.

exactly	adv.
exact	adj.

exam	n.
examine	v.

excited	adj.
excite	v.
excitedly	adv.
excitement	n.
exciting	adj.

expensive	adj.
expense	n.
inexpensive	adj.

experience	n.
experienced	adj.

explain	v.
explanation	n.

explore	v.
explorer	n.`;

            const lines = rawData.split('\n').filter(line => line.trim());
            const groups = [];
            let currentGroup = [];
            let groupName = '';

            for (let line of lines) {
                if (line.trim() === '') continue;

                const parts = line.split('\t').filter(p => p.trim());
                if (parts.length >= 2) {
                    const word = parts[0].trim();
                    const pos = parts[1].trim();

                    if (currentGroup.length === 0) {
                        groupName = word + '系列';
                    }

                    currentGroup.push({ word, pos });

                    // 检查是否是组的结束（下一行为空或者是新的词根）
                    const nextLineIndex = lines.indexOf(line) + 1;
                    if (nextLineIndex >= lines.length ||
                        lines[nextLineIndex].trim() === '' ||
                        (nextLineIndex < lines.length && !isRelatedWord(word, lines[nextLineIndex].split('\t')[0]))) {

                        if (currentGroup.length > 1) {
                            groups.push({
                                group: groupName,
                                words: [...currentGroup]
                            });
                        }
                        currentGroup = [];
                    }
                }
            }

            return groups;
        }

        function isRelatedWord(baseWord, newWord) {
            // 简单的相关性检查
            const base = baseWord.toLowerCase();
            const word = newWord.toLowerCase();

            return word.includes(base) || base.includes(word) ||
                   word.startsWith(base.substring(0, 3)) ||
                   base.startsWith(word.substring(0, 3));
        }

        const wordData = parseWordData();

        // 练习状态
        let currentMode = 'practice';
        let currentQuestion = 0;
        let correctCount = 0;
        let wrongCount = 0;
        let currentWordGroup = null;
        let currentTargetWord = null;

        // DOM元素
        const modeButtons = document.querySelectorAll('.mode-btn');
        const modeContents = document.querySelectorAll('.mode-content');
        const currentWordEl = document.getElementById('current-word');
        const posHintEl = document.getElementById('pos-hint');
        const answerInput = document.getElementById('answer-input');
        const checkBtn = document.getElementById('check-btn');
        const skipBtn = document.getElementById('skip-btn');
        const nextBtn = document.getElementById('next-btn');
        const feedbackEl = document.getElementById('feedback');
        const correctCountEl = document.getElementById('correct-count');
        const wrongCountEl = document.getElementById('wrong-count');
        const accuracyEl = document.getElementById('accuracy');
        const progressFill = document.getElementById('progress-fill');

        // 初始化
        init();

        function init() {
            setupEventListeners();
            generateNextQuestion();
            updateStats();
        }

        function setupEventListeners() {
            // 模式切换
            modeButtons.forEach(btn => {
                btn.addEventListener('click', () => switchMode(btn.dataset.mode));
            });

            // 答案检查
            checkBtn.addEventListener('click', checkAnswer);
            nextBtn.addEventListener('click', generateNextQuestion);
            skipBtn.addEventListener('click', skipQuestion);

            // 回车键提交
            answerInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    if (checkBtn.style.display !== 'none') {
                        checkAnswer();
                    } else {
                        generateNextQuestion();
                    }
                }
            });
        }

        function switchMode(mode) {
            currentMode = mode;
            
            // 更新按钮状态
            modeButtons.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.mode === mode);
            });

            // 显示对应内容
            if (mode === 'review') {
                document.getElementById('practice-mode').style.display = 'none';
                document.getElementById('review-mode').style.display = 'block';
                showReviewMode();
            } else {
                document.getElementById('practice-mode').style.display = 'block';
                document.getElementById('review-mode').style.display = 'none';
                generateNextQuestion();
            }
        }

        function generateNextQuestion() {
            // 随机选择一个词汇组
            const randomGroup = wordData[Math.floor(Math.random() * wordData.length)];
            currentWordGroup = randomGroup;
            
            // 随机选择组内的一个词作为题目
            const sourceWord = randomGroup.words[Math.floor(Math.random() * randomGroup.words.length)];
            
            // 随机选择组内的另一个词作为答案
            const targetWords = randomGroup.words.filter(w => w.word !== sourceWord.word);
            if (targetWords.length === 0) return generateNextQuestion();
            
            currentTargetWord = targetWords[Math.floor(Math.random() * targetWords.length)];
            
            // 更新显示
            currentWordEl.textContent = sourceWord.word;
            posHintEl.textContent = `请写出其${getPosName(currentTargetWord.pos)}形式`;
            
            // 重置输入
            answerInput.value = '';
            answerInput.focus();
            
            // 重置按钮状态
            checkBtn.style.display = 'inline-block';
            nextBtn.style.display = 'none';
            feedbackEl.style.display = 'none';
            
            currentQuestion++;
            updateProgress();
        }

        function checkAnswer() {
            const userAnswer = answerInput.value.trim().toLowerCase();
            const correctAnswer = currentTargetWord.word.toLowerCase();
            
            if (userAnswer === correctAnswer) {
                showFeedback(true, `正确！${currentTargetWord.word} (${currentTargetWord.meaning})`);
                correctCount++;
            } else {
                showFeedback(false, `错误！正确答案是：${currentTargetWord.word} (${currentTargetWord.meaning})`);
                wrongCount++;
            }
            
            updateStats();
            checkBtn.style.display = 'none';
            nextBtn.style.display = 'inline-block';
            nextBtn.focus();
        }

        function skipQuestion() {
            showFeedback(false, `跳过！正确答案是：${currentTargetWord.word} (${currentTargetWord.meaning})`);
            wrongCount++;
            updateStats();
            checkBtn.style.display = 'none';
            nextBtn.style.display = 'inline-block';
        }

        function showFeedback(isCorrect, message) {
            feedbackEl.textContent = message;
            feedbackEl.className = `feedback ${isCorrect ? 'correct' : 'incorrect'}`;
            feedbackEl.style.display = 'block';
        }

        function updateStats() {
            correctCountEl.textContent = correctCount;
            wrongCountEl.textContent = wrongCount;
            
            const total = correctCount + wrongCount;
            const accuracy = total > 0 ? Math.round((correctCount / total) * 100) : 0;
            accuracyEl.textContent = accuracy + '%';
        }

        function updateProgress() {
            const progress = Math.min((currentQuestion / 20) * 100, 100);
            progressFill.style.width = progress + '%';
        }

        function getPosName(pos) {
            const posMap = {
                'n.': '名词',
                'v.': '动词', 
                'adj.': '形容词',
                'adv.': '副词',
                'prep.': '介词',
                'prep. & adv.': '介词/副词',
                'v. & n.': '动词/名词',
                'adj. & n.': '形容词/名词'
            };
            return posMap[pos] || pos;
        }

        function showReviewMode() {
            const reviewContainer = document.getElementById('word-groups');
            reviewContainer.innerHTML = '';
            
            wordData.forEach(group => {
                const groupDiv = document.createElement('div');
                groupDiv.className = 'word-group';
                
                const title = document.createElement('h3');
                title.textContent = group.group;
                groupDiv.appendChild(title);
                
                const wordList = document.createElement('div');
                wordList.className = 'word-list';
                
                group.words.forEach(word => {
                    const wordItem = document.createElement('div');
                    wordItem.className = 'word-item';
                    wordItem.innerHTML = `<strong>${word.word}</strong> <em>${word.pos}</em><br><small>${word.meaning}</small>`;
                    wordList.appendChild(wordItem);
                });
                
                groupDiv.appendChild(wordList);
                reviewContainer.appendChild(groupDiv);
            });
        }
    </script>
</body>
</html>
